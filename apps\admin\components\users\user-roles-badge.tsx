"use client";

import { useI18n } from "@curatd/shared/locales/client";
import { Badge } from "@curatd/ui/components/badge";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON>Content,
  <PERSON><PERSON>ipProvider,
  TooltipTrigger,
} from "@curatd/ui/components/tooltip";
import { Enums } from "@curatd/backend";

interface UserRolesBadgeProps {
  roles: Enums.Roles[];
}

export function UserRolesBadge({ roles }: UserRolesBadgeProps) {
  const t = useI18n();

  // Helper function to get translated role name
  const getRoleTranslation = (role: string) => {
    try {
      return t(`admin.userManagement.roleTypes.${role}` as any, {}) || role;
    } catch {
      return role;
    }
  };

  if (roles.length === 0) {
    return (
      <span className="text-sm text-muted-foreground">
        {t("admin.userManagement.noRoles")}
      </span>
    );
  }

  // If 2 or fewer roles, show them all
  if (roles.length <= 2) {
    return (
      <div className="flex flex-wrap gap-1">
        {roles.map((role, index) => (
          <Badge
            key={`${index}-${role || index}`}
            variant="secondary"
            className="text-xs"
          >
            {getRoleTranslation(role || "Unknown Role")}
          </Badge>
        ))}
      </div>
    );
  }

  // If more than 2 roles, show first role + count with tooltip
  const firstRole = roles[0];
  const remainingCount = roles.length - 1;
  const allRolesText = roles
    .map((role) => getRoleTranslation(role || "Unknown"))
    .join(", ");

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex flex-wrap gap-1 cursor-help">
            <Badge variant="secondary" className="text-xs">
              {getRoleTranslation(firstRole || "Unknown")}
            </Badge>
            <Badge variant="outline" className="text-xs">
              +{remainingCount}
            </Badge>
          </div>
        </TooltipTrigger>
        <TooltipContent variant="muted">
          <p>{allRolesText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
