import { roleValidator } from "../../../../enumValidators";
import { doc } from "convex-helpers/validators";
import { Infer, v } from "convex/values";
import { Doc } from "../../../../../convex/_generated/dataModel"
import schema from "../../../../../convex/schema";

/*──────────────────────── GENERAL ────────────────────────*/

export const invitationResponseDTOValidator = doc(schema, "invitations").fields;
export const invitationResponseDTOObjectValidator = v.object(invitationResponseDTOValidator);

export interface IInvitation extends Doc<"invitations"> {
}

/* ──────────────────────── LIST ────────────────────────*/

// Validator for the request body
export const InvitationsListRequestDTOValidator = {
    search: v.string(),
    role: v.array(roleValidator),
    limit: v.number(),
    offset: v.number(),
}

// Validator for the response body

export const InvitationsListResponseDTOValidator = {
    data: v.array(invitationResponseDTOObjectValidator),
    total: v.number(),
}

// Extra validators to generate the types from the validators
export const InvitationsListRequestDTOObjectValidator = v.object(InvitationsListRequestDTOValidator);
export const InvitationsListResponseDTOObjectValidator = v.object(InvitationsListResponseDTOValidator);

// Type for the request body

export type InvitationsListRequestDTO = Infer<typeof InvitationsListRequestDTOObjectValidator>;
export type InvitationsListResponseDTO = Infer<typeof InvitationsListResponseDTOObjectValidator>;

/*──────────────────────── GET ────────────────────────*/

// Validator for the request body

export const InvitationsGetRequestDTOValidator = {
    id: v.id("invitations"),
}

// Validator for the response body

export const InvitationsGetResponseDTOValidator = invitationResponseDTOValidator

// Extra validators to generate the types from the validators
export const InvitationsGetRequestDTOObjectValidator = v.object(InvitationsGetRequestDTOValidator);
export const InvitationsGetResponseDTOObjectValidator = v.object(InvitationsGetResponseDTOValidator);

// Type for the request body

export type InvitationsGetRequestDTO = Infer<typeof InvitationsGetRequestDTOObjectValidator>;
export type InvitationsGetResponseDTO = Infer<typeof InvitationsGetResponseDTOObjectValidator>;

/*──────────────────────── SEND INVITATION EMAIL ACTION ────────────────────────*/

// Validator for the request body

export const SendInvitationEmailRequestDTOValidator = {
    email: v.string(),
    token: v.string(),
    inviterName: v.optional(v.string()),
    role: roleValidator,
}

// Validator for the response body

export const SendInvitationEmailResponseDTOValidator = {
    success: v.boolean(),
    emailId: v.optional(v.string()),
    error: v.optional(v.string()),
}

// Extra validators to generate the types from the validators
export const SendInvitationEmailRequestDTOObjectValidator = v.object(SendInvitationEmailRequestDTOValidator);
export const SendInvitationEmailResponseDTOObjectValidator = v.object(SendInvitationEmailResponseDTOValidator);

// Type for the request body

export type SendInvitationEmailRequestDTO = Infer<typeof SendInvitationEmailRequestDTOObjectValidator>;
export type SendInvitationEmailResponseDTO = Infer<typeof SendInvitationEmailResponseDTOObjectValidator>;

/*──────────────────────── INVITE ────────────────────────*/

// Validator for the request body

export const InvitationsInviteRequestDTOValidator = {
    email: v.string(),
    role: roleValidator,
}

// Validator for the response body

export const InvitationsInviteResponseDTOValidator = {
    success: v.boolean(),
    message: v.string(),
}

// Extra validators to generate the types from the validators
export const InvitationsInviteRequestDTOObjectValidator = v.object(InvitationsInviteRequestDTOValidator);
export const InvitationsInviteResponseDTOObjectValidator = v.object(InvitationsInviteResponseDTOValidator);

// Type for the request body

export type InvitationsInviteRequestDTO = Infer<typeof InvitationsInviteRequestDTOObjectValidator>;
export type InvitationsInviteResponseDTO = Infer<typeof InvitationsInviteResponseDTOObjectValidator>;

/*──────────────────────── CANCEL ────────────────────────*/

// Validator for the request body

export const InvitationsCancelRequestDTOValidator = {
    id: v.id("invitations"),
}

// Validator for the response body

export const InvitationsCancelResponseDTOValidator = {
    success: v.boolean(),
    message: v.string(),
}

// Extra validators to generate the types from the validators
export const InvitationsCancelRequestDTOObjectValidator = v.object(InvitationsCancelRequestDTOValidator);
export const InvitationsCancelResponseDTOObjectValidator = v.object(InvitationsCancelResponseDTOValidator);

// Type for the request body

export type InvitationsCancelRequestDTO = Infer<typeof InvitationsCancelRequestDTOObjectValidator>;
export type InvitationsCancelResponseDTO = Infer<typeof InvitationsCancelResponseDTOObjectValidator>;

/*──────────────────────── RESEND ────────────────────────*/

// Validator for the request body

export const InvitationsResendRequestDTOValidator = {
    id: v.id("invitations"),
}

// Validator for the response body

export const InvitationsResendResponseDTOValidator = {
    success: v.boolean(),
    message: v.string(),
}

// Extra validators to generate the types from the validators
export const InvitationsResendRequestDTOObjectValidator = v.object(InvitationsResendRequestDTOValidator);
export const InvitationsResendResponseDTOObjectValidator = v.object(InvitationsResendResponseDTOValidator);

// Type for the request body

export type InvitationsResendRequestDTO = Infer<typeof InvitationsResendRequestDTOObjectValidator>;
export type InvitationsResendResponseDTO = Infer<typeof InvitationsResendResponseDTOObjectValidator>;

/*──────────────────────── BULK INVITE ────────────────────────*/

// Validator for the request body

export const InvitationsBulkInviteRequestDTOValidator = {
    emails: v.array(v.string()),
    role: roleValidator,
}

// Validator for the response body

export const InvitationsBulkInviteResponseDTOValidator = {
    success: v.boolean(),
    message: v.string(),
    invitedUsers: v.optional(v.number()),
    existingUsers: v.optional(v.number()),
}

// Extra validators to generate the types from the validators
export const InvitationsBulkInviteRequestDTOObjectValidator = v.object(InvitationsBulkInviteRequestDTOValidator);
export const InvitationsBulkInviteResponseDTOObjectValidator = v.object(InvitationsBulkInviteResponseDTOValidator);

// Type for the request body

export type InvitationsBulkInviteRequestDTO = Infer<typeof InvitationsBulkInviteRequestDTOObjectValidator>;
export type InvitationsBulkInviteResponseDTO = Infer<typeof InvitationsBulkInviteResponseDTOObjectValidator>;

/*──────────────────────── BULK CANCEL ────────────────────────*/

// Validator for the request body

export const InvitationsBulkCancelRequestDTOValidator = {
    ids: v.array(v.id("invitations")),
}

// Validator for the response body

export const InvitationsBulkCancelResponseDTOValidator = {
    success: v.boolean(),
    message: v.string(),
}

// Extra validators to generate the types from the validators
export const InvitationsBulkCancelRequestDTOObjectValidator = v.object(InvitationsBulkCancelRequestDTOValidator);
export const InvitationsBulkCancelResponseDTOObjectValidator = v.object(InvitationsBulkCancelResponseDTOValidator);

// Type for the request body

export type InvitationsBulkCancelRequestDTO = Infer<typeof InvitationsBulkCancelRequestDTOObjectValidator>;
export type InvitationsBulkCancelResponseDTO = Infer<typeof InvitationsBulkCancelResponseDTOObjectValidator>;

/*──────────────────────── BULK RESEND ────────────────────────*/

// Validator for the request body

export const InvitationsBulkResendRequestDTOValidator = {
    ids: v.array(v.id("invitations")),
}

// Validator for the response body

export const InvitationsBulkResendResponseDTOValidator = {
    success: v.boolean(),
    message: v.string(),
    count: v.optional(v.number()),
}

// Extra validators to generate the types from the validators
export const InvitationsBulkResendRequestDTOObjectValidator = v.object(InvitationsBulkResendRequestDTOValidator);
export const InvitationsBulkResendResponseDTOObjectValidator = v.object(InvitationsBulkResendResponseDTOValidator);

// Type for the request body

export type InvitationsBulkResendRequestDTO = Infer<typeof InvitationsBulkResendRequestDTOObjectValidator>;
export type InvitationsBulkResendResponseDTO = Infer<typeof InvitationsBulkResendResponseDTOObjectValidator>;

