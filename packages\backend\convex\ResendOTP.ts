import Resend from "@auth/core/providers/resend";
import { Email } from "@convex-dev/auth/providers/Email";
import { Resend as ResendAPI } from "resend";
import { alphabet, generateRandomString } from "oslo/crypto";
import { EmailConfig, EmailUserConfig } from "@convex-dev/auth/server";
import { GenericDataModel } from "convex/server";

const providerBaseConfig = {
    apiKey: process.env.RESEND_API_KEY,
    async generateVerificationToken() {
        return generateRandomString(8, alphabet("0-9"));
    },
    maxAge: 60 * 15, // 15 minutes
    async sendVerificationRequest({ identifier: email, provider, token }) {
        const resend = new ResendAPI(provider.apiKey);
        const { error } = await resend.emails.send({
            from: "Curatd <<EMAIL>>",
            to: [email],
            subject: `${token} is your Curatd one-time password`,
            text: "Your one-time password is " + token,
        });

        if (error) {
            throw new Error("Could not send");
        }
    },
} as EmailUserConfig & Pick<EmailConfig<GenericDataModel>, "sendVerificationRequest">;


export const ResendOTPPasswordReset = Resend({
    id: "otp-password-reset",
    ...providerBaseConfig,
});

export const ResendOTP = Email({
    id: "otp",
    ...providerBaseConfig,
});