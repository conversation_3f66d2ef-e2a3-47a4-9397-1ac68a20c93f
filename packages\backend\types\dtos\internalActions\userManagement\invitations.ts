import { v } from "convex/values";
import { Infer } from "convex/values";
import { invitationResponseDTOObjectValidator } from "../../useCases";
import { Nullable } from "../../../../helpers/nullableObjectReturnValidator";

/*──────────────────────── PROCESS USER INVITATIONS ────────────────────────*/

// Validator for the request body

export const ProcessUserInvitationsRequestDTOValidator = {
    userId: v.id("users"),
    email: v.string(),
}

// Validator for the response body

export const ProcessUserInvitationsResponseDTOValidator = {
    success: v.boolean(),
    message: v.string(),
}

// Extra validators to generate the types from the validators

export const ProcessUserInvitationsRequestDTOObjectValidator = v.object(ProcessUserInvitationsRequestDTOValidator);
export const ProcessUserInvitationsResponseDTOObjectValidator = v.object(ProcessUserInvitationsResponseDTOValidator);

// Type for the request body

export type ProcessUserInvitationsRequestDTO = Infer<typeof ProcessUserInvitationsRequestDTOObjectValidator>;
export type ProcessUserInvitationsResponseDTO = Infer<typeof ProcessUserInvitationsResponseDTOObjectValidator>;
