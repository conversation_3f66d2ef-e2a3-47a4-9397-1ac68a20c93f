export default {
    admin: {
        nav: {
            dashboard: 'Dashboard',
            users: 'Users',
            invitations: 'Invitations',
            join: 'Join',
        },
        user: {
            account: 'Account',
            logout: 'Logout',
        },
        userManagement: {
            title: 'User Management',
            description: 'Manage user accounts, roles, and permissions',
            inviteUser: 'Invite User',
            search: 'Search users...',
            allRoles: 'All roles',
            allStatuses: 'All statuses',
            active: 'Active',
            inactive: 'Inactive',
            invitationPending: 'Invitation Pending',
            invitationExpired: 'Invitation Expired',
            invitationCancelled: 'Invitation Cancelled',
            invitationAccepted: 'Invitation Accepted',
            filters: 'filter',
            filters_plural: 'filters',
            applied: 'applied',
            applied_plural: 'applied',
            clear: 'Clear',
            noFiltersApplied: 'No filters applied',
            totalUsers: 'total users',
            columns: 'Columns',
            loadingUsers: 'Loading users...',
            noUsersFound: 'No users found.',
            failedToLoad: 'Failed to load users',
            retry: 'Retry',
            user: 'User',
            roles: 'Roles',
            noRoles: 'No roles',
            status: 'Status',
            created: 'Created',
            actions: 'Actions',
            editProfile: 'Edit Profile',
            manageRoles: 'Manage Roles',
            resendInvitation: 'Resend Invitation',
            activate: 'Activate',
            deactivate: 'Deactivate',
            delete: 'Delete',
            userActivated: 'User activated successfully',
            userDeactivated: 'User deactivated successfully',
            userDeleted: 'User deleted successfully',
            failedToUpdateStatus: 'Failed to update user status',
            failedToDelete: 'Failed to delete user',
            confirmDelete: 'Are you sure you want to delete this user?',
            rowsPerPage: 'Rows per page',
            page: 'Page',
            of: 'of',
            goToFirstPage: 'Go to first page',
            goToPreviousPage: 'Go to previous page',
            goToNextPage: 'Go to next page',
            goToLastPage: 'Go to last page',
            inviteDialog: {
                title: 'Invite User',
                description: 'Send an invitation to a new user. They will receive an email with instructions to set up their account.',
                emailLabel: 'Email Address',
                emailPlaceholder: '<EMAIL>',
                displayNameLabel: 'Display Name (Optional)',
                displayNamePlaceholder: 'John Doe',
                rolesLabel: 'Roles',
                selectedRoles: 'Selected roles:',
                loadingRoles: 'Loading roles...',
                cancel: 'Cancel',
                sending: 'Sending...',
                sendInvitation: 'Send Invitation',
                invitationSent: 'User invitation sent successfully',
                invitationFailed: 'Failed to send invitation',
                pleaseEnterValidEmail: 'Please enter a valid email address',
                pleaseSelectAtLeastOneRole: 'Please select at least one role',
                errors: {
                    userAlreadyExists: 'A user with this email address already exists',
                    invalidEmail: 'Please enter a valid email address',
                    missingRoles: 'Please select at least one role',
                    networkError: 'Network error. Please check your connection and try again',
                    serverError: 'Server error. Please try again later',
                    authenticationRequired: 'Authentication required. Please sign in again',
                    insufficientPermissions: 'You don\'t have permission to invite users',
                    invalidRoles: 'One or more selected roles are invalid',
                    emailConfigurationError: 'Email service is not configured properly. Please contact support',
                    configurationError: 'System configuration error. Please contact support',
                    genericError: 'An unexpected error occurred. Please try again',
                    validationError: 'Please check your input and try again',
                    supabaseError: 'Service temporarily unavailable. Please try again later',
                    notFound: 'The requested resource was not found',
                    rateLimitExceeded: 'Too many requests. Please wait a moment and try again',
                },
            },
            manageRolesDialog: {
                title: 'Manage Roles for {userName}',
                description: 'Assign or remove roles for this user.',
                cancel: 'Cancel',
                saving: 'Saving...',
                saveChanges: 'Save Changes',
                successMessage: 'Roles have been updated successfully.',
                errorMessage: 'An error occurred while updating roles.'
            },
            bulkActions: {
                confirmDeactivation: 'Are you sure you want to deactivate {count} user?',
                confirmDeactivation_plural: 'Are you sure you want to deactivate {count} users?',
                deactivationSuccess: '{count} user has been successfully deactivated.',
                deactivationSuccess_plural: '{count} users have been successfully deactivated.',
                deactivationError: 'Failed to deactivate users.',
                selected: '{count} of {total} row selected.',
                selected_plural: '{count} of {total} rows selected.',
                editRoles: 'Edit Roles',
                delete: 'Delete'
            },
            bulkManageRolesDialog: {
                title: 'Bulk Edit Roles for {count} User',
                title_plural: 'Bulk Edit Roles for {count} Users',
                description: 'This will ADD the selected roles to all selected users. It will not remove their existing roles.',
                assignButton: 'Assign Roles',
                noRoleSelectedError: 'Please select at least one role to assign.',
                successMessage: 'Roles successfully assigned to {count} user.',
                successMessage_plural: 'Roles successfully assigned to {count} users.',
                errorMessage: 'An error occurred while bulk updating roles.'
            },
            stripeStatus: 'Stripe Status',
            stripeStatuses: {
                onboarding: 'Onboarding',
                active: 'Active',
                inactive: 'Inactive',
                restricted: 'Restricted',
                not_connected: 'Not Connected',
            },
            profileTypes: {
                coach: 'Coach',
                facilityManager: 'Facility Manager',
            },
            roleTypes: {
                admin: 'Admin',
                facility_manager: 'Facility Manager',
                coach: 'Coach',
                customer: 'Customer',
            },
        },
        invitationManagement: {
            title: 'Invitation Management',
            description: 'Manage user invitations and track their status',
            inviteUser: 'Invite User',
            search: 'Search invitations...',
            allRoles: 'All roles',
            filters: 'filter',
            filters_plural: 'filters',
            applied: 'applied',
            applied_plural: 'applied',
            clear: 'Clear',
            noFiltersApplied: 'No filters applied',
            noInvitationsFound: 'No invitations found.',
            email: 'Email',
            role: 'Role',
            status: 'Status',
            createdAt: 'Created',
            resendInvitation: 'Resend Invitation',
            cancelInvitation: 'Cancel Invitation',
            bulkResend: 'Resend',
            bulkCancel: 'Cancel',
            confirmCancel: 'Are you sure you want to cancel this invitation?',
            invitationCancelled: 'Invitation cancelled successfully',
            invitationResent: 'Invitation resent successfully',
            failedToCancelInvitation: 'Failed to cancel invitation',
            failedToResendInvitation: 'Failed to resend invitation',
            statusTypes: {
                pending: 'Pending',
                accepted: 'Accepted',
            },
            bulkCancelDialog: {
                title: 'Cancel Invitations',
                description: 'Are you sure you want to cancel {count} invitation?',
                description_plural: 'Are you sure you want to cancel {count} invitations?',
                cancel: 'Cancel',
                cancelling: 'Cancelling...',
                confirmCancel: 'Cancel Invitations',
                successMessage: 'Successfully cancelled {count} invitation',
                successMessage_plural: 'Successfully cancelled {count} invitations',
                errorMessage: 'Failed to cancel invitations',
                noInvitationsSelectedError: 'Please select at least one invitation to cancel',
            },
            bulkResendDialog: {
                title: 'Resend Invitations',
                description: 'Are you sure you want to resend {count} invitation?',
                description_plural: 'Are you sure you want to resend {count} invitations?',
                cancel: 'Cancel',
                resending: 'Resending...',
                confirmResend: 'Resend Invitations',
                successMessage: 'Successfully resent {count} invitation',
                successMessage_plural: 'Successfully resent {count} invitations',
                errorMessage: 'Failed to resend invitations',
                noInvitationsSelectedError: 'Please select at least one invitation to resend',
            },
        },
        acceptInvitation: {
            title: 'Complete Your Account Setup',
            description: 'Set up your password to access the admin dashboard',
            displayNameLabel: 'Display Name',
            displayNamePlaceholder: 'Enter your full name',
            passwordLabel: 'Password',
            passwordPlaceholder: 'Enter your password',
            confirmPasswordLabel: 'Confirm Password',
            confirmPasswordPlaceholder: 'Confirm your password',
            acceptButton: 'Complete Setup',
            accepting: 'Setting up account...',
            loading: 'Loading...',
            success: 'Account setup complete! Redirecting to dashboard...',
            invalidToken: 'Invalid or expired invitation link',
            failedToAccept: 'Failed to complete account setup',
            passwordMinLength: 'Password must be at least 6 characters',
            passwordsDoNotMatch: 'Passwords do not match',
        },
        unauthorized: {
            title: 'Access Denied',
            description: 'You don\'t have permission to access the admin dashboard. This area is restricted to platform administrators only.',
            needAccess: 'Need access?',
            contactAdmin: 'Contact a platform administrator to request access to the admin dashboard.',
            returnToMain: 'Return to Main Site',
        }
    },
    user: {
        default_name: 'User',
    },
    web: {
        acceptInvitation: {
            title: 'Join Curatd',
            description: 'Complete your account setup to start booking workouts',
            displayNameLabel: 'Display Name',
            displayNamePlaceholder: 'Enter your full name',
            passwordLabel: 'Password',
            passwordPlaceholder: 'Enter your password',
            confirmPasswordLabel: 'Confirm Password',
            confirmPasswordPlaceholder: 'Confirm your password',
            acceptButton: 'Join Now',
            accepting: 'Setting up account...',
            loading: 'Loading...',
            success: 'Welcome to Curatd! Redirecting to your dashboard...',
            invalidToken: 'Invalid or expired invitation link',
            failedToAccept: 'Failed to complete account setup',
            passwordMinLength: 'Password must be at least 6 characters',
            passwordsDoNotMatch: 'Passwords do not match',
        },
    },
    onboarding: {
        title: 'Welcome to Curatd',
        subtitle: 'Choose your role to get started with your fitness journey',
        description: 'Select the option that best describes you to personalize your experience',
        roleSelection: {
            title: 'What best describes you?',
            subtitle: 'This helps us personalize your experience',
            regularUser: {
                title: 'I\'m here to train',
                description: 'Book workouts, find trainers, and achieve your fitness goals',
                features: {
                    bookWorkouts: 'Book workouts instantly',
                    findCertifiedTrainers: 'Find certified trainers',
                    trackProgress: 'Track your progress',
                    joinGroupSessions: 'Join group sessions'
                },
            },
            coach: {
                title: 'I\'m a coach',
                description: 'Share your expertise and help others reach their fitness goals',
                features: {
                    createTrainingPrograms: 'Create training programs',
                    manageSchedule: 'Manage your schedule',
                    acceptPayments: 'Accept payments',
                    buildClientBase: 'Build your client base'
                },
            },
            facilityManager: {
                title: 'I manage a facility',
                description: 'List your space and connect with trainers and athletes',
                features: {
                    listFacility: 'List your facility',
                    manageBookings: 'Manage bookings',
                    partnerWithTrainers: 'Partner with trainers',
                    earnRevenue: 'Earn revenue'
                },
            },
            buttons: {
                continue: 'Continue',
                getStarted: 'Get Started',
                back: 'Back',
                skip: 'Skip for now',
                selectRole: 'Select this role',
                selected: 'Selected'
            },
            steps: {
                roleSelection: 'Role Selection',
                profileSetup: 'Profile Setup',
                preferences: 'Preferences',
                complete: 'Complete'
            },
            completion: {
                title: 'You\'re all set!',
                subtitle: 'Welcome to your personalized fitness experience',
                description: 'Start exploring everything Curatd has to offer',
                cta: 'Start Exploring'
            },
        },
    },
    nav: {
        brand: 'curatd.',
        athlete: 'For Athletes',
        coach: 'For Coaches',
        gym: 'For Gyms',
        contact: 'Contact',
        language: 'Language',
        openMenu: 'Open menu',
        closeMenu: 'Close menu'
    },
    landing: {
        hero: {
            title: 'Private wellness. Anywhere, anytime.',
            subtitle: 'Book personal sessions at home, outdoors, or in select partner gyms.',
            cta: 'Register now'
        },
        carousel: {
            title: 'Sessions on offer',
            items: {
                strength: {
                    title: 'Strength',
                    subtitle: 'Build muscle, power, and confidence.'
                },
                cardio: {
                    title: 'Cardio',
                    subtitle: 'Boost endurance and torch calories with high-energy sessions.'
                },
                flow: {
                    title: 'Flow',
                    subtitle: 'Reconnect mind and body with yoga, Pilates, or movement-based training.'
                },
                relax: {
                    title: 'Relax',
                    subtitle: 'Recover, decompress, and recharge with therapeutic massage.g'
                }
            },
        },
        faq: {
            title: 'Frequently Asked Questions',
            subtitle: 'Got questions? We\'ve got answers. Find everything you need to know about booking your workouts.',
            contactCta: 'Contact Support',
            contactSubtitle: 'Still have questions?',
            questions: {
                inclusivity: {
                    question: 'Is Curatd suitable for seniors, pregnant women, people with disabilities, or those training in groups?',
                    answer: 'Absolutely! Curatd is designed for everyone. Our certified coaches are specially trained to adapt sessions to all profiles and needs - whether you\'re a senior, pregnant, have a disability, or want to train with friends or family. We believe fitness should be accessible to all, and each coach personalizes workouts to ensure a safe, effective, and enjoyable experience for every individual.'
                },
                howItWorks: {
                    question: 'How does the booking system work?',
                    answer: 'Our booking system is designed to be simple and fast. Just browse available workouts, select your preferred time slot, and confirm your booking in under 30 seconds. You\'ll receive instant confirmation and all the details you need.'
                },
                locations: {
                    question: 'Where can I book workouts?',
                    answer: 'You can book workouts anywhere - at home, outdoors in parks, or at partner gym facilities. Whether you\'re traveling for business or staying local, our platform connects you with certified trainers in all cities where we operate.'
                },
                workoutTypes: {
                    question: 'What types of workouts are available?',
                    answer: 'We offer strength training, cardio, yoga, pilates, HIIT, boxing, dance fitness, and more. Whether you\'re a beginner or advanced athlete, each coach is trained to adapt sessions to your fitness level, age, and any special circumstances.'
                },
                equipment: {
                    question: 'Do I need to bring any equipment?',
                    answer: 'No equipment needed! Our coaches come fully equipped with everything required for your workout. For gym sessions, you\'ll have access to all facility equipment, and outdoor workouts are designed to use minimal or no equipment.'
                },
                companions: {
                    question: 'Can I bring someone to my workout?',
                    answer: 'Absolutely! Bringing a friend, partner, or family member is encouraged. Training together can be more motivating and fun. Just let us know when booking so we can ensure the session accommodates everyone.'
                },
                cancellation: {
                    question: 'What\'s your cancellation and booking policy?',
                    answer: 'You can book workouts up to 30 days in advance and cancel or reschedule up to 24 hours before your session without penalty. Cancellations within 24 hours may incur a small fee depending on the trainer\'s policy.'
                },
                pricing: {
                    question: 'How much do sessions cost?',
                    answer: 'Sessions start from $25 per class when paying individually. With our subscription plan, you can save up to 40% and pay as little as $15 per session, plus get priority booking access to your favorite trainers.'
                }
            }
        },
        howItWorks: {
            title: 'How It Works',
            subtitle: '4 steps to your next workout',
            cta: 'Start now',
            steps: {
                selectSportDuration: {
                    title: 'Sign Up',
                    description: 'Create your profile in seconds and start exploring tailored sessions near you.',
                },
                chooseCoach: {
                    title: 'Choose Your Flow',
                    description: 'Select your discipline, location, time, and the coach that fits your vibe',
                },
                bookLocation: {
                    title: 'Customize & Book',
                    description: 'Add a friend, select options, and check out in one tap.',
                },
                payAndGo: {
                    title: 'Sweat it out',
                    description: 'Meet your coach, show up, and feel amazing — wherever you are.',
                }
            },
        },
        places: {
            title: 'Train',
            titleHighlight: 'Anywhere',
            subtitle: 'Whether you\'re at home, traveling for business or leisure — enjoy access to private sessions outdoors or at our partner locations.',
            items: {
                atHome: {
                    title: 'We come to you.',
                    subtitle: 'At Home',
                    description: 'Turn your living room into your personal gym — ideal for privacy and tight schedules.',
                    features: {
                        comfort: 'Ultimate comfort and convenience',
                        flexibility: 'Train on your own schedule',
                        privacy: 'Bring a friend or train solo'
                    }
                },
                publicSpaces: {
                    title: 'The world is your gym.',
                    subtitle: 'Outdoors',
                    description: 'Train in parks, by the river, on rooftops — wherever moves you.',
                    features: {
                        travel: 'Business trips and vacation workouts',
                        global: 'Local trainers in major cities worldwide',
                        flexible: 'Hotel gyms, parks, or outdoor spaces'
                    }
                },
                partnerFacilities: {
                    title: 'We unlock exclusive training spaces.',
                    subtitle: 'Partner facilities',
                    description: 'Use a hotel, coworking club, or premium facility as your workout base.',
                    features: {
                        equipment: 'All equipment provided',
                        community: 'Train solo or with companions',
                        professional: 'Expert supervision available'
                    }
                }
            }
        },
        subscription: {
            title: 'Our approach',
            subtitle: 'Pay per class, or unlock exclusive savings and benefits with our monthly subscription.',
            payPerClass: {
                title: 'Pay as You Go',
                price: 'From 50€',
                priceUnit: 'per session',
                description: 'Perfect if you want no strings attached.',
                features: {
                    access: 'No contracts, no hidden fees',
                    booking: 'Book any session, anytime',
                    flexibility: '100% flexibility',
                },
                cta: 'Registration'
            },
            subscription: {
                title: 'Curatd+',
                price: '19.99€',
                priceUnit: 'Monthly subscription',
                description: 'A smart way to stay fit and save money.',
                features: {
                    discount: '15% off all sessions',
                    priority: 'Priority booking during peak hours',
                    exclusive: 'Free equipment rental',
                    invites: 'Invites to members-only events'
                },
                cta: 'Subscribe'
            },
            learnMore: 'Learn More',
            benefits: {
                smartSavings: {
                    title: 'Smart Savings',
                    description: 'The more you train, the more you save with our subscription model'
                },
                priorityAccess: {
                    title: 'Priority Access',
                    description: 'Book your favorite trainers and time slots before everyone else'
                },
                noCommitment: {
                    title: 'No Commitment',
                    description: 'Pause or cancel anytime with complete flexibility'
                }
            }
        },
        contact: {
            title: 'Get in Touch',
            subtitle: 'Have questions or want to learn more? We\'d love to hear from you. Send us a message and we\'ll get back to you as soon as possible.',
            getInTouch: 'Get in Touch',
            email: '<EMAIL>',
            phone: '+****************',
            address: '123 Fitness Street, Health City, HC 12345',
            followUs: 'Follow Us',
            sendMessage: 'Send Message',
            namePlaceholder: 'Your Name',
            emailPlaceholder: '<EMAIL>',
            subjectPlaceholder: 'Subject',
            messagePlaceholder: 'Tell us about your question or how we can help you...',
            sending: 'Sending...'
        },
        joinNow: {
            title: 'Join the Waitlist',
            subtitle: 'Be the first to access Curatd when we launch.',
            formTitle: 'Get early access, exclusive perks, and VIP booking when we go live.',
            emailPlaceholder: 'Enter your email address',
            submitButton: 'Join Waitlist',
            submitting: 'Joining...',
            disclaimer: 'We respect your privacy and won\'t spam you. Unsubscribe at any time.',
            successTitle: 'You\'re In!',
            successMessage: 'Thank you for joining our waitlist. We\'ll be in touch soon with exclusive updates.',
            benefits: {
                earlyAccess: {
                    title: 'Early Access',
                    description: 'Be among the first to try our platform before public launch'
                },
                specialOffer: {
                    title: 'Special Offers',
                    description: 'Get exclusive discounts and promotional rates for early adopters'
                },
                updates: {
                    title: 'Latest Updates',
                    description: 'Stay informed about new features, coaches, and locations'
                }
            }
        },
        coach: {
            hero: {
                title: 'Get more clients. Grow your business — on your terms',
                subtitle: 'Join a premium platform built for top coaches. Set your schedule, work flexibly, and earn more.',
                cta: 'Apply to become a Curatd Coach.'
            },
            benefits: {
                title: 'Why Join Curatd?',
                items: {
                    flexibility: {
                        badge: 'Flexibility & Freedom',
                        title: 'Total flexibility',
                        subtitle: 'Set your own schedule',
                        description: 'Coach where and when it works for you'
                    },
                    travelWork: {
                        badge: 'Travel & Work',
                        title: 'Work from anywhere',
                        subtitle: 'Our model lets you coach clients in any city we operate in',
                        description: 'Travel and earn — with a trusted platform behind you'
                    },
                    expertise: {
                        badge: 'Quality Clients & Earnings',
                        title: 'We bring the clients to you',
                        subtitle: 'Work with serious, motivated individuals',
                        description: 'Competitive rates and performance-based bonuses'
                    },
                    growth: {
                        badge: 'Visibility & Growth',
                        title: 'We handle marketing and bookings',
                        subtitle: 'You focus on coaching',
                        description: 'Grow your personal brand and reach new audiences'
                    }
                }
            },
            howItWorks: {
                title: 'How it works',
                steps: {
                    signUp: {
                        title: 'Sign Up',
                        description: 'Share your location, sport, experience and availability.',
                    },
                    getVerified: {
                        title: 'Get verified',
                        description: 'We review your profile and qualifications. If you’re a fit, we help you set up your profile and onboarding.',
                    },
                    startCoaching: {
                        title: 'Start coaching',
                        description: 'Set your schedule and pricing. Get matched with clients. Start earning on your terms.',
                    }
                }
            },
            signup: {
                title: 'Ready to join the Curatd Crew?',
                subtitle: 'Fill in the form and we’ll be in touch to get you started.',
                formTitle: 'Join as a Coach',
                formDescription: 'Start your journey with our coaching platform',
                form: {
                    fullName: {
                        label: 'Full Name (optional)',
                        placeholder: 'Enter your full name'
                    },
                    email: {
                        label: 'Email Address',
                        placeholder: 'Enter your email address',
                        required: true
                    },
                    sportTypes: {
                        label: 'Type(s) of sport you coach',
                        placeholder: 'e.g., Tennis, Basketball, Swimming, Personal Training...',
                        required: true
                    }
                },
                cta: 'Join now',
                submitting: 'Submitting...',
                privacyNote: 'We respect your privacy. Your information is only used to contact you about joining our platform.',
                success: 'Thank you! We\'ll contact you soon to get started.',
                errors: {
                    fillRequired: 'Please fill in all required fields',
                    tryAgain: 'Something went wrong. Please try again.'
                }
            }
        },
        gym: {
            hero: {
                title: 'Unlock new revenue. Maximize usage of your gym or wellness space.',
                subtitle: 'Partner with Curatd and welcome premium clients and top-tier coaches — on your terms.',
                cta: 'List your space'
            },
            benefits: {
                title: 'Why Join Curatd?',
                items: {
                    fillYourGym: {
                        badge: 'Monetise Unused Hours',
                        title: 'Make the most of off-peak time',
                        description: 'List your space for select hours',
                        subtitle: 'Generate income without extra staff or equipment'
                    },
                    earnEveryTime: {
                        badge: 'Premium Exposure',
                        title: 'Attract high-value, wellness-conscious clients',
                        description: 'Be part of a curated platform of premium partners',
                        subtitle: 'Increase visibility in your city and online'
                    },
                    freePromotion: {
                        badge: 'Easy Integration',
                        title: 'No contracts or long-term commitments',
                        description: 'Flexible scheduling via our platform',
                        subtitle: 'We handle bookings, payments, and support'
                    },
                    simpleFlexible: {
                        badge: 'Trusted Network',
                        title: 'Work only with verified coaches and insured sessions',
                        description: 'All guests are pre-screened and professional',
                        subtitle: 'A curated, respectful ecosystem'
                    }
                },
            },
            howItWorks: {
                title: 'How it works',
                steps: {
                    registerGym: {
                        title: 'Sign up and tell us about your facility',
                        description: '1. Share your location, type of space, hours of availability.'
                    },
                    review: {
                        title: 'We review your profile and verify the space',
                        description: '2. We’ll review your application and get back to you within 24 hours.'
                    },
                    welcomeUsers: {
                        title: 'Welcome coaches and clients at your convenience',
                        description: '3. Earn passive income every time your space is used.'
                    }
                }
            },
            callToAction: {
                title: 'Ready to list your space with Curatd?',
                subtitle: 'Fill out the form and we’ll get in touch.',
                formTitle: 'Register your space today',
                formDescription: 'Ready to bring more people to your space and grow your business? Fill in the form below — it only takes 2 minutes.',
                form: {
                    gymName: {
                        label: 'Space name',
                        placeholder: 'Enter your space name',
                        required: true
                    },
                    email: {
                        label: 'Email address',
                        placeholder: 'Enter your email address',
                        required: true
                    },
                    address: {
                        label: 'Address/location of the space',
                        placeholder: 'Enter your space address',
                        required: true
                    },
                    phone: {
                        label: 'Phone number',
                        placeholder: 'Enter your phone number',
                        required: false
                    },
                    message: {
                        label: 'Message or description',
                        placeholder: 'Tell us about your space...',
                        required: false
                    }
                },
                cta: 'Register now',
                privacyNote: 'We respect your privacy. Your information is only used to contact you about joining our platform.'
            }
        },
        footer: {
            brand: {
                title: 'curatd.',
                description: 'Your Private Sessions'
            },
            quickLinks: {
                title: 'Quick Links',
                forAthletes: 'For Athletes',
                forCoaches: 'For Coaches',
                forGyms: 'For Gyms',
                contact: 'Contact'
            },
            connect: {
                title: 'Connect',
                instagram: 'Instagram',
                linkedin: 'LinkedIn'
            },
            legal: {
                title: 'Legal',
                copyright: 'Curatd. All rights reserved.',
                privacyPolicy: 'Privacy Policy',
                termsOfService: 'Terms of Service',
                cookies: 'Cookie Policy'
            },
            bottomBar: {
                copyright: ' Curatd. All rights reserved.',
                madeBy: 'Made with <3 byThe Tech Nation',
            }
        },
        preRegistration: {
            errors: {
                fillRequired: "Please fill in all required fields",
                emailRequired: "Please enter your email",
            },
            submitting: "Submitting...",
            // User (join-now)
            user: {
                email: {
                    placeholder: "Enter your email address",
                },
                cta: "Join Now",
                submitting: "Submitting...",
                successTitle: "You're In!",
                successMessage: "Thank you for joining our waitlist. We'll keep you updated on our progress!",
                duplicateTitle: "Already Registered!",
                duplicateMessage: "You're already on our list. We'll keep you updated on our progress!",
                privacyNote: "We respect your privacy. No spam, just updates on our progress.",
            },
            // Coach
            coach: {
                fullName: {
                    label: "Full Name (optional)",
                    placeholder: "Enter your full name",
                },
                email: {
                    label: "Email Address",
                    placeholder: "Enter your email address",
                },
                sportTypes: {
                    label: "Type(s) of sport you coach",
                    placeholder: "e.g., Tennis, Basketball, Swimming, Personal Training...",
                },
                cta: "Join now",
                successTitle: "Welcome Aboard!",
                successMessage: "Thank you for joining! We'll contact you soon to get started.",
                duplicateTitle: "Already Registered!",
                duplicateMessage: "You're already registered as a coach! We'll keep you updated on our progress.",
            },
            // Gym
            gym: {
                gymName: {
                    label: "Space Name",
                    placeholder: "Your space's name",
                },
                email: {
                    label: "Email Address",
                    placeholder: "<EMAIL>",
                },
                address: {
                    label: "Space Address",
                    placeholder: "123 Main St, City, State",
                },
                phone: {
                    label: "Phone Number",
                    placeholder: "(*************",
                },
                message: {
                    label: "Tell us about your space",
                    placeholder: "Number of members, current challenges, what you're looking for...",
                },
                cta: "Get Started",
                successTitle: "Thank You!",
                successMessage: "We'll be in touch within 24 hours to discuss how Curatd can help your space.",
                duplicateTitle: "Already Registered!",
                duplicateMessage: "Your space is already registered! We'll keep you updated on our progress.",
            },
        }
    },
    privacy: {
        title: 'Privacy Policy',
        lastUpdated: 'Last updated',
        sections: {
            introduction: {
                title: 'Introduction',
                content: 'Welcome to Curatd. We respect your privacy and are committed to protecting your personal data. This privacy policy will inform you about how we look after your personal data when you visit our website and tell you about your privacy rights and how the law protects you.'
            },
            dataCollection: {
                title: 'What Data We Collect',
                content: 'We may collect, use, store and transfer different kinds of personal data about you which we have grouped together as follows:',
                items: [
                    'Identity Data: includes first name, maiden name, last name, username or similar identifier, marital status, title, date of birth and gender.',
                    'Contact Data: includes billing address, delivery address, email address and telephone numbers.',
                    'Technical Data: includes internet protocol (IP) address, your login data, browser type and version, time zone setting and location, browser plug-in types and versions, operating system and platform.',
                    'Profile Data: includes your username and password, purchases or orders made by you, your interests, preferences, feedback and survey responses.',
                    'Usage Data: includes information about how you use our website, products and services.',
                    'Marketing and Communications Data: includes your preferences in receiving marketing from us and our third parties and your communication preferences.'
                ]
            },
            dataUse: {
                title: 'How We Use Your Data',
                content: 'We will only use your personal data when the law allows us to. Most commonly, we will use your personal data in the following circumstances:',
                items: [
                    'To provide and maintain our service',
                    'To notify you about changes to our service',
                    'To allow you to participate in interactive features of our service when you choose to do so',
                    'To provide customer care and support',
                    'To provide analysis or valuable information so that we can improve the service',
                    'To monitor the usage of the service',
                    'To detect, prevent and address technical issues'
                ]
            },
            dataSharing: {
                title: 'Data Sharing',
                content: 'We do not sell, trade, or otherwise transfer your personal data to third parties without your consent, except as described in this policy. We may share your information with:',
                items: [
                    'Service providers who assist us in operating our website and conducting our business',
                    'Professional advisers including lawyers, bankers, auditors and insurers',
                    'Government bodies that require us to report processing activities'
                ]
            },
            dataSecurity: {
                title: 'Data Security',
                content: 'We have put in place appropriate security measures to prevent your personal data from being accidentally lost, used or accessed in an unauthorised way, altered or disclosed. We limit access to your personal data to those employees, agents, contractors and other third parties who have a business need to know.'
            },
            yourRights: {
                title: 'Your Rights',
                content: 'Under certain circumstances, you have rights under data protection laws in relation to your personal data:',
                items: [
                    'Request access to your personal data',
                    'Request correction of your personal data',
                    'Request erasure of your personal data',
                    'Object to processing of your personal data',
                    'Request restriction of processing your personal data',
                    'Request transfer of your personal data',
                    'Right to withdraw consent'
                ]
            },
            contact: {
                title: 'Contact Us',
                content: 'If you have any questions about this Privacy Policy, please contact us by <NAME_EMAIL> or through our contact form on the website.'
            }
        },
        backToHome: 'Back to Home'
    },
    login: {
        title: "Welcome (back)!",
        description: "Sign in to your account to continue!",
        subdescription: "Don't have an account yet? It will be created automatically!",
        admin: {
            title: "Admin Login",
            description: "Sign in to the admin dashboard",
            subdescription: "Admin access only - contact support if you need access",
        },
        email: {
            label: "Email",
            placeholder: "Enter your email",
            send_code: "Send verification code",
            sending: "Sending code...",
        },
        otp: {
            title: "Check your email",
            description: "We sent a verification code to",
            label: "Verification code",
            verify: "Verify code",
            verifying: "Verifying...",
            back: "Back to email",
        },
        social: {
            continue_with: "Or continue with",
        },
        errors: {
            generic: "An error occurred",
            invalid_email: "Please enter a valid email address",
            incomplete_otp: "Please enter the complete 6-digit code",
            send_otp_failed: "Failed to send OTP",
            verify_otp_failed: "Invalid verification code",
        },
        loading: "Logging in...",
    },
    logout: {
        title: "Logout",
        description: "Are you sure you want to logout?",
        confirm: "Logout",
        cancel: "Cancel",
    },
    business: {
        nav: {
            dashboard: 'Dashboard',
            generalProfile: 'General Profile',
            otherPage: 'Other Page',
            switchEntity: 'Switch Entity',
            coach: 'Coach',
            facilityManager: 'Facility Manager',
        },
        entityDisplay: {
            coach: 'Coach Profile',
            facilityManager: 'Facility Manager Profile',
            id: 'ID: {id}',
        },
    },
    errors: {
        404: {
            title: "Oops! Page Not Found",
            subtitle: "The page you're looking for seems to have wandered off on its own fitness journey.",
            description: "The page you're looking for seems to have wandered off on its own fitness journey. Let's get you back on track!",
            goHome: "Go Home",
            goBack: "Go Back",
            lookingFor: "Looking for something specific?",
            athleteDesc: "Book personal training sessions",
            coachDesc: "Join and grow your business",
            gymDesc: "Partner with us",
            startTraining: "Start Training",
            becomeCoach: "Become a Coach",
            partnerNow: "Partner Now",
        }
    },
} as const;