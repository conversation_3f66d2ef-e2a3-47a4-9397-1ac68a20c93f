import { Badge } from "@curatd/ui/components/badge";
import { useI18n } from "@curatd/shared/locales/client";
import { IconCheck, IconClock } from "@tabler/icons-react";

interface InvitationStatusBadgeProps {
  accepted: boolean;
}

export function InvitationStatusBadge({ accepted }: InvitationStatusBadgeProps) {
  const t = useI18n();

  if (accepted) {
    return (
      <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
        <IconCheck className="mr-1 h-3 w-3" />
        {t("admin.invitationManagement.status.accepted")}
      </Badge>
    );
  }

  return (
    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
      <IconClock className="mr-1 h-3 w-3" />
      {t("admin.invitationManagement.status.pending")}
    </Badge>
  );
}
