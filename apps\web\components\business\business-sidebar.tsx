"use client";

import * as React from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";

import { NavMain } from "@curatd/ui/components/nav-main";
import { NavUser } from "@curatd/ui/components/nav-user";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@curatd/ui/components/sidebar";
import CuratdLogo from "@curatd/ui/components/curatd-logo";
import useCurrentUser from "@curatd/ui/hooks/use-current-user";
import { useCurrentLocale } from "@curatd/shared/locales/client";
import { useBusinessNavigation } from "@/hooks/use-business-navigation";
import { EntitySwitcher } from "@/components/business/entity-switcher";

export function BusinessSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const currentUser = useCurrentUser();
  const locale = useCurrentLocale();
  const pathname = usePathname();

  const {
    availableEntities,
    activeEntityInfo,
    navItems,
    isNavItemActive,
    switchEntity,
    hasMultipleEntities,
  } = useBusinessNavigation();

  const data = {
    user: {
      name: currentUser.name,
      email: currentUser.email,
      avatar: currentUser.avatar,
    },
    navMain: navItems,
  };

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-0"
            >
              <Link href={`/${locale}/app/business`}>
                <CuratdLogo showIcon variant="business" />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain
          items={data.navMain}
          pathname={pathname}
          isNavItemActive={isNavItemActive}
        />
      </SidebarContent>
      <SidebarFooter>
        <EntitySwitcher
          availableEntities={availableEntities}
          activeEntityInfo={activeEntityInfo}
          switchEntity={switchEntity}
          hasMultipleEntities={hasMultipleEntities}
        />
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
