"use client";

import * as React from "react";
import { IconUser<PERSON>lus, IconFilter } from "@tabler/icons-react";
import { useI18n } from "@curatd/shared/locales/client";
import { Button } from "@curatd/ui/components/button";
import { Badge } from "@curatd/ui/components/badge";
import { InvitationFilters } from "@/components/invitations/invitation-filters";
import { InvitationDataTable } from "@/components/invitations/invitation-data-table";
import { InviteUserDialog } from "@/components/invitations/invite-user-dialog";
import { Enums } from "@curatd/backend";

export default function InvitationsPage() {
  const t = useI18n();
  const [searchTerm, setSearchTerm] = React.useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = React.useState("");
  const [roleFilter, setRoleFilter] = React.useState<Enums.Roles[]>([]);
  const [inviteDialogOpen, setInviteDialogOpen] = React.useState(false);

  // Debounce search term
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const hasActiveFilters = roleFilter.length > 0 || searchTerm.trim() !== "";
  const activeFiltersCount = [
    roleFilter.length > 0,
    searchTerm.trim() !== "",
  ].filter(Boolean).length;

  return (
    <div className="flex flex-col h-full">
      <div className="border-b p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h1 className="text-3xl font-bold">
              {t("admin.invitationManagement.title")}
            </h1>
            {hasActiveFilters && (
              <Badge variant="secondary" className="text-xs font-medium">
                <IconFilter className="mr-1 h-3 w-3" />
                {activeFiltersCount}
              </Badge>
            )}
          </div>
          <Button onClick={() => setInviteDialogOpen(true)}>
            <IconUserPlus className="mr-2 h-4 w-4" />
            {t("admin.invitationManagement.inviteUser")}
          </Button>
        </div>
        <p className="text-muted-foreground mt-2">
          {t("admin.invitationManagement.description")}
        </p>
      </div>

      <InvitationFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        roleFilter={roleFilter}
        onRoleFilterChange={setRoleFilter}
      />

      <div className="flex-1 p-6">
        <InvitationDataTable
          searchTerm={debouncedSearchTerm}
          roleFilter={roleFilter}
        />
      </div>

      <InviteUserDialog
        open={inviteDialogOpen}
        onOpenChange={setInviteDialogOpen}
        onSuccess={() => {
          // Optionally refresh the data or show a success message
        }}
      />
    </div>
  );
}
