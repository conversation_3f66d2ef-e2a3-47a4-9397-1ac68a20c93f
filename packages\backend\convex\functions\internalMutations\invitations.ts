import { ProcessUserInvitationsRequestDTOValidator, ProcessUserInvitationsResponseDTOValidator } from "../../../types/dtos";
import { internalMutation } from "../../_generated/server";


/**
 * Internal action to process pending invitation for a newly created user
 * This acts as a "trigger" after user creation to automatically accept matching invitation
 */
export const processUserInvitation = internalMutation({
    args: ProcessUserInvitationsRequestDTOValidator,
    returns: ProcessUserInvitationsResponseDTOValidator,
    handler: async (ctx, args) => {
        console.log(`Processing invitation for user ${args.userId} with email ${args.email}`);

        // Find pending invitation for this email
        const pendingInvitation = await ctx.db
            .query("invitations")
            .withIndex("by_email", q => q.eq("email", args.email))
            .filter(q => q.eq(q.field("accepted"), false))
            .unique();

        if (!pendingInvitation) {
            return {
                success: true,
                message: "invitations.none_found",
            };
        }

        // Accept the invitation
        await ctx.db.patch(pendingInvitation._id, {
            accepted: true,
            acceptedUserId: args.userId,
            acceptedAt: new Date().toISOString(),
        });

        return {
            success: true,
            message: "invitation.accepted",
        };
    },
});
