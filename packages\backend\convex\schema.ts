/*************************************************************************
 * CURATD – Convex Schema (Production)
 * FINAL VERSION – July 2025
 *
 * This schema powers the CURATD high-end sport coaching marketplace.
 * It is designed for scale, flexibility, legal compliance, auditability,
 * internationalization, multi-vendor e-commerce, and advanced analytics.
 *
 *************************************************************************/

import { defineSchema, defineTable } from "convex/server";
import { authTables } from "@convex-dev/auth/server";
import {
    addonsTableValidator,
    affiliationCodesTableValidator,
    affiliationUsesTableValidator,
    buddyPassesTableValidator,
    calendarEventsTableValidator,
    calendarsTableValidator,
    cartsTableValidator,
    chatMessageAttachmentsTableValidator,
    chatMessageReactionsTableValidator,
    chatMessagesTableValidator,
    chatParticipantsTableValidator,
    chatThreadsTableValidator,
    coachesTableValidator,
    customersTableValidator,
    discountsTableValidator,
    discountUsagesTableValidator,
    facilitiesTableValidator,
    facilityAssetsTableValidator,
    facilityManagersTableValidator,
    invitationsTableValidator,
    invoicesTableValidator,
    membershipPlansTableValidator,
    notificationsTableValidator,
    ordersTableValidator,
    pointsOfInterestsTableValidator,
    productsTableValidator,
    productVariantsTableValidator,
    reviewsTableValidator,
    sessionInstancesTableValidator,
    subscriptionsTableValidator,
    userEventsTableValidator,
    userMediaTableValidator,
    usersTableValidator, waitingListTableValidator
} from "../types/tableValidators"

const invitations = defineTable(invitationsTableValidator)
    .index("by_email", ["email"])
    .index("by_token", ["token"])
    .index("by_roles", ["roleSuggested"])
    .searchIndex("search_email", {
        searchField: "email",
        filterFields: ["accepted", "roleSuggested"]
    });

const users = defineTable(usersTableValidator)
    .index("email", ["email"])
    .index("by_roles", ["roles"])
    .searchIndex("search_email", {
        searchField: "email",
        filterFields: ["roles"]
    });

const customers = defineTable(customersTableValidator).index("by_user", ["userId"]);

const coaches = defineTable(coachesTableValidator).index("by_user", ["userId"]);

const facilityManagers = defineTable(facilityManagersTableValidator)
    .index("by_user", ["userId"])
    .index("by_kyc_status", ["kyc.verificationStatus"]);

const facilities = defineTable(facilitiesTableValidator).index("by_manager", ["facilityManagerId"]);

const facilityAssets = defineTable(facilityAssetsTableValidator).index("by_facility", ["facilityId"]);

const membershipPlans = defineTable(membershipPlansTableValidator).index("by_key", ["key"]);

const subscriptions = defineTable(subscriptionsTableValidator).index("by_customer", ["customerId"]);

const buddyPasses = defineTable(buddyPassesTableValidator).index("by_subscription", ["subscriptionId"]);

const products = defineTable(productsTableValidator).index("by_coach", ["coachId"]);

const productVariants = defineTable(productVariantsTableValidator).index("by_product", ["productId"]);

const addons = defineTable(addonsTableValidator).index("by_owner", ["ownerType", "ownerId"]);

const pointsOfInterests = defineTable(pointsOfInterestsTableValidator).index("by_creator", ["creatorType", "creatorId"]);

const calendars = defineTable(calendarsTableValidator);

const calendarEvents = defineTable(calendarEventsTableValidator).index("by_calendar", ["calendarId"]);

const sessionInstances = defineTable(sessionInstancesTableValidator).index("by_variant_start", ["productVariantId", "startTime"]);

const carts = defineTable(cartsTableValidator).index("by_customer", ["customerId"]);

const orders = defineTable(ordersTableValidator).index("by_customer", ["customerId"]);

const invoices = defineTable(invoicesTableValidator).index("by_customer", ["customerId"]);

const affiliationCodes = defineTable(affiliationCodesTableValidator).index("by_code", ["code"]);

const affiliationUses = defineTable(affiliationUsesTableValidator).index("by_code", ["codeId"]);

const discounts = defineTable(discountsTableValidator).index("by_key", ["key"]);

const discountUsages = defineTable(discountUsagesTableValidator).index("by_discount", ["discountKey"]);

const chatThreads = defineTable(chatThreadsTableValidator);

const chatParticipants = defineTable(chatParticipantsTableValidator).index("by_thread", ["threadId"]);

const chatMessages = defineTable(chatMessagesTableValidator).index("by_thread", ["threadId"]);

const chatMessageReactions = defineTable(chatMessageReactionsTableValidator).index("by_message", ["messageId"]);

const chatMessageAttachments = defineTable(chatMessageAttachmentsTableValidator).index("by_message", ["messageId"]);

const userMedia = defineTable(userMediaTableValidator).index("by_user", ["userId"]);

const reviews = defineTable(reviewsTableValidator).index("by_coach", ["coachId"]);

const notifications = defineTable(notificationsTableValidator)
    .index("by_user_status", ["userId", "status"])
    .index("by_status", ["status"]);

const userEvents = defineTable(userEventsTableValidator)
    .index("by_user", ["userId", "createdAt"])
    .index("by_seller", ["sellerType", "sellerId", "createdAt"]);

const waitingList = defineTable(waitingListTableValidator)
    .index("by_email", ["email"])
    .index("by_intent", ["intent", "createdAt"]);

/*──────────────────────── EXPORT (required by Convex) ─────────*/
export default defineSchema({
    ...authTables,
    invitations,
    users,
    customers,
    coaches,
    facilityManagers,
    facilities,
    facilityAssets,
    membershipPlans,
    subscriptions,
    buddyPasses,
    products,
    productVariants,
    addons,
    pointsOfInterests,
    calendars,
    calendarEvents,
    sessionInstances,
    carts,
    orders,
    invoices,
    affiliationCodes,
    affiliationUses,
    discounts,
    discountUsages,
    chatThreads,
    chatParticipants,
    chatMessages,
    chatMessageReactions,
    chatMessageAttachments,
    userMedia,
    reviews,
    notifications,
    userEvents,
    waitingList
});
