import { Doc } from "../../convex/_generated/dataModel";
import { QueryCtx, MutationCtx } from "../../convex/_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

export async function getCurrentUser<T extends QueryCtx | MutationCtx>(ctx: T): Promise<Doc<"users"> | null> {
    const userId = await getAuthUserId(ctx);
    if (userId === null) {
        return null;
    }
    return await ctx.db.get(userId);
}



