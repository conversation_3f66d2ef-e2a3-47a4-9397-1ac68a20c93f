"use node";

import { internalAction } from "../../_generated/server";
import { sendEmail } from "@curatd/shared/resend";
import { getAppUrlsConfig } from "@curatd/shared/config";
import { SendInvitationEmailRequestDTOObjectValidator, SendInvitationEmailResponseDTOObjectValidator } from "../../../types/dtos";
import { Roles } from "../../../types/enums";

/**
 * Internal action to send an invitation email to a user
 * This is a Convex internal action that handles sending invitation emails via Resend
 */
export const sendInvitationEmail = internalAction({
    args: SendInvitationEmailRequestDTOObjectValidator,
    returns: SendInvitationEmailResponseDTOObjectValidator,
    handler: async (ctx, args) => {
        try {
            const { NEXT_PUBLIC_WEB_APP_URL, NEXT_PUBLIC_ADMIN_APP_URL } = getAppUrlsConfig();

            const baseUrl = args.role === Roles.ADMIN ? NEXT_PUBLIC_ADMIN_APP_URL : NEXT_PUBLIC_WEB_APP_URL;

            // Create the invitation acceptance URL
            const invitationUrl = `${baseUrl}/auth/accept-invitation?token=${args.token}&email=${encodeURIComponent(args.email)}`;

            // Create the email content
            const inviterText = args.inviterName ? `${args.inviterName} has invited you` : "You have been invited";

            let roleText = "";
            if (args.role) {
                switch (args.role) {
                    case Roles.ADMIN:
                        roleText = " as an admin";
                        break;
                    case Roles.FACILITY_MANAGER:
                        roleText = " as a facility manager";
                        break;
                    case Roles.COACH:
                        roleText = " as a coach";
                        break;
                    case Roles.CUSTOMER:
                        roleText = " as a customer";
                        break;
                }
            }

            const html = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #333; margin-bottom: 10px;">You're Invited to Curatd!</h1>
                    </div>
                    
                    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                        <p style="margin: 0; font-size: 16px; color: #333;">
                            ${inviterText} to join Curatd${roleText}.
                        </p>
                    </div>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="${invitationUrl}" 
                           style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                            Accept Invitation
                        </a>
                    </div>
                    
                    <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px;">
                        <p style="color: #666; font-size: 14px; margin-bottom: 10px;">
                            If the button above doesn't work, you can copy and paste this link into your browser:
                        </p>
                        <p style="color: #007bff; font-size: 14px; word-break: break-all;">
                            ${invitationUrl}
                        </p>
                    </div>
                    
                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                        <p style="color: #666; font-size: 12px; margin: 0;">
                            This invitation was sent to ${args.email}. If you didn't expect this invitation, you can safely ignore this email.
                        </p>
                    </div>
                </div>
            `;

            const text = `
You're Invited to Curatd!

${inviterText} to join Curatd${roleText}.

To accept your invitation, visit: ${invitationUrl}

If you didn't expect this invitation, you can safely ignore this email.

This invitation was sent to ${args.email}.
            `.trim();

            // Send the email using the shared Resend helper
            const result = await sendEmail({
                from: "Curatd <<EMAIL>>", // You may need to update this with your verified domain
                to: args.email,
                subject: "You're invited to join Curatd!",
                html,
                text,
                tags: [
                    { name: "type", value: "invitation" },
                    { name: "role", value: args.role || "unknown" }
                ]
            });

            return {
                success: true,
                emailId: result.id,
            };
        } catch (error) {
            console.error("Failed to send invitation email:", error);
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error occurred",
            };
        }
    },
});
