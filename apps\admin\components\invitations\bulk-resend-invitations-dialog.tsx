import * as React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@curatd/ui/components/dialog";
import { Button } from "@curatd/ui/components/button";
import { api } from "@curatd/backend/api";
import { Doc } from "@curatd/backend/schema";
import { useMutation } from "convex/react";
import { toast } from "sonner";
import { useI18n } from "@curatd/shared/locales/client";

interface BulkResendInvitationsDialogProps {
  invitationIds: Doc<"invitations">["_id"][];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function BulkResendInvitationsDialog({
  invitationIds,
  open,
  onOpenChange,
  onSuccess,
}: BulkResendInvitationsDialogProps) {
  const t = useI18n();
  const bulkResendInvitations = useMutation(
    api.functions.useCases.admin.userManagement.invitations.bulkResend
  );

  const [isResending, setIsResending] = React.useState(false);

  const handleResend = async () => {
    if (invitationIds.length === 0) {
      toast.error(
        t("admin.invitationManagement.bulkResendDialog.noInvitationsSelectedError")
      );
      return;
    }

    setIsResending(true);
    try {
      const result = await bulkResendInvitations({
        ids: invitationIds,
      });

      if (result.success) {
        toast.success(
          t("admin.invitationManagement.bulkResendDialog.successMessage", {
            count: result.count || invitationIds.length,
          })
        );
        onSuccess();
        onOpenChange(false);
      } else {
        toast.error(result.message || t("admin.invitationManagement.bulkResendDialog.errorMessage"));
      }
    } catch (error) {
      console.error("Failed to resend invitations:", error);
      toast.error(t("admin.invitationManagement.bulkResendDialog.errorMessage"));
    } finally {
      setIsResending(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {t("admin.invitationManagement.bulkResendDialog.title")}
          </DialogTitle>
          <DialogDescription>
            {t("admin.invitationManagement.bulkResendDialog.description", {
              count: invitationIds.length,
            })}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isResending}
          >
            {t("admin.invitationManagement.bulkResendDialog.cancel")}
          </Button>
          <Button
            type="button"
            onClick={handleResend}
            disabled={isResending}
          >
            {isResending
              ? t("admin.invitationManagement.bulkResendDialog.resending")
              : t("admin.invitationManagement.bulkResendDialog.confirmResend")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
