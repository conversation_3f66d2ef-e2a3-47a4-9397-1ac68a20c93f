import { QueryCtx, query } from "../../../../convex/_generated/server";
import { InvitationsGetRequestDTO, InvitationsGetRequestDTOObjectValidator, InvitationsGetResponseDTO, InvitationsGetResponseDTOValidator } from "../../../../types/dtos";
import { withLoggedIn } from "../../../../wrappers/auth/withLoggedIn";

export const inspect = query({
    args: InvitationsGetRequestDTOObjectValidator,
    returns: InvitationsGetResponseDTOValidator,
    handler: withLoggedIn(async (ctx: QueryCtx, user, args: InvitationsGetRequestDTO): Promise<InvitationsGetResponseDTO> => {
        const invitation = await ctx.db.get<"invitations">(args.id);
        if (!invitation || invitation.accepted || invitation.email !== user.email) {
            throw new Error("Invitation not found");
        }
        return invitation;
    })

})

