import * as React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@curatd/ui/components/dialog";
import { Button } from "@curatd/ui/components/button";
import { api } from "@curatd/backend/api";
import { Doc } from "@curatd/backend/schema";
import { useMutation } from "convex/react";
import { toast } from "sonner";
import { useI18n } from "@curatd/shared/locales/client";

interface BulkCancelInvitationsDialogProps {
  invitationIds: Doc<"invitations">["_id"][];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function BulkCancelInvitationsDialog({
  invitationIds,
  open,
  onOpenChange,
  onSuccess,
}: BulkCancelInvitationsDialogProps) {
  const t = useI18n();
  const bulkCancelInvitations = useMutation(
    api.functions.useCases.admin.userManagement.invitations.bulkCancel
  );

  const [isCancelling, setIsCancelling] = React.useState(false);

  const handleCancel = async () => {
    if (invitationIds.length === 0) {
      toast.error(
        t("admin.invitationManagement.bulkCancelDialog.noInvitationsSelectedError")
      );
      return;
    }

    setIsCancelling(true);
    try {
      const result = await bulkCancelInvitations({
        ids: invitationIds,
      });

      if (result.success) {
        toast.success(
          t("admin.invitationManagement.bulkCancelDialog.successMessage", {
            count: invitationIds.length,
          })
        );
        onSuccess();
        onOpenChange(false);
      } else {
        toast.error(result.message || t("admin.invitationManagement.bulkCancelDialog.errorMessage"));
      }
    } catch (error) {
      console.error("Failed to cancel invitations:", error);
      toast.error(t("admin.invitationManagement.bulkCancelDialog.errorMessage"));
    } finally {
      setIsCancelling(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {t("admin.invitationManagement.bulkCancelDialog.title")}
          </DialogTitle>
          <DialogDescription>
            {t("admin.invitationManagement.bulkCancelDialog.description", {
              count: invitationIds.length,
            })}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isCancelling}
          >
            {t("admin.invitationManagement.bulkCancelDialog.cancel")}
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleCancel}
            disabled={isCancelling}
          >
            {isCancelling
              ? t("admin.invitationManagement.bulkCancelDialog.cancelling")
              : t("admin.invitationManagement.bulkCancelDialog.confirmCancel")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
