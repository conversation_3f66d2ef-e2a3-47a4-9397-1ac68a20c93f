import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { useI18n } from "@curatd/shared/locales/client";
import { useMutation } from "convex/react";
import { api } from "@curatd/backend/api";

import { Button } from "@curatd/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@curatd/ui/components/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@curatd/ui/components/form";
import { Input } from "@curatd/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@curatd/ui/components/select";
import { Enums } from "@curatd/backend";

const USER_ROLES = Object.values(Enums.Roles);

type InviteUserForm = {
  email: string;
  role: Enums.Roles;
};

interface InviteUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function InviteUserDialog({
  open,
  onOpenChange,
  onSuccess,
}: InviteUserDialogProps) {
  const t = useI18n();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const inviteUserSchemaWithI18n = z.object({
    email: z
      .string()
      .email(t("admin.userManagement.inviteDialog.pleaseEnterValidEmail")),
    role: z
      .string()
      .min(
        1,
        t("admin.userManagement.inviteDialog.pleaseSelectAtLeastOneRole")
      ),
  });

  const form = useForm<InviteUserForm>({
    resolver: zodResolver(inviteUserSchemaWithI18n),
    defaultValues: {
      email: "",
      role: "" as Enums.Roles,
    },
  });

  const inviteUser = useMutation(
    api.functions.useCases.admin.userManagement.invitations.invite
  );

  const handleSubmit = async (data: InviteUserForm) => {
    setIsSubmitting(true);
    try {
      const result = await inviteUser({
        email: data.email,
        role: data.role,
      });

      if (result.success) {
        toast.success(t("admin.userManagement.inviteDialog.invitationSent"));
        form.reset();
        onSuccess?.();
        onOpenChange(false);
      } else {
        // Handle backend error messages
        let errorMessage = t(
          "admin.userManagement.inviteDialog.errors.genericError"
        );

        if (result.message === "invitation.user_already_exists") {
          errorMessage = t(
            "admin.userManagement.inviteDialog.errors.userAlreadyExists"
          );
        } else if (result.message === "user.not_found") {
          errorMessage = t(
            "admin.userManagement.inviteDialog.errors.authenticationRequired"
          );
        }

        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Failed to send invitation:", error);
      toast.error(t("admin.userManagement.inviteDialog.errors.genericError"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {t("admin.userManagement.inviteDialog.title")}
          </DialogTitle>
          <DialogDescription>
            {t("admin.userManagement.inviteDialog.description")}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <div className="space-y-4">
              {/* Email */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("admin.userManagement.inviteDialog.emailLabel")} *
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t(
                          "admin.userManagement.inviteDialog.emailPlaceholder"
                        )}
                        type="email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Role */}
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("admin.userManagement.inviteDialog.rolesLabel")} *
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {USER_ROLES.map((role) => (
                          <SelectItem key={role} value={role}>
                            {role}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                {t("admin.userManagement.inviteDialog.cancel")}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? t("admin.userManagement.inviteDialog.sending")
                  : t("admin.userManagement.inviteDialog.sendInvitation")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
