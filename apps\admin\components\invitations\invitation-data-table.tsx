"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import {
  IconChevronLeft,
  IconChevronRight,
  IconChevronsLeft,
  IconChevronsRight,
  IconDotsVertical,
  IconMail,
  IconTrash,
  IconRefresh,
} from "@tabler/icons-react";
import { toast } from "sonner";
import { useI18n } from "@curatd/shared/locales/client";

import { Badge } from "@curatd/ui/components/badge";
import { Button } from "@curatd/ui/components/button";
import { Checkbox } from "@curatd/ui/components/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@curatd/ui/components/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@curatd/ui/components/table";

import { useQuery, useMutation } from "convex/react";
import { Enums } from "@curatd/backend";
import { api } from "@curatd/backend/api";
import { DTOs } from "@curatd/backend";

import { InvitationStatusBadge } from "./invitation-status-badge";
import { BulkCancelInvitationsDialog } from "./bulk-cancel-invitations-dialog";
import { BulkResendInvitationsDialog } from "./bulk-resend-invitations-dialog";

interface InvitationDataTableProps {
  searchTerm?: string;
  roleFilter?: Enums.Roles[];
}

export function InvitationDataTable({
  searchTerm,
  roleFilter,
}: InvitationDataTableProps) {
  const t = useI18n();
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [isBulkCancelDialogOpen, setIsBulkCancelDialogOpen] =
    React.useState(false);
  const [isBulkResendDialogOpen, setIsBulkResendDialogOpen] =
    React.useState(false);

  // Use the invitation management hooks
  const invitationsData = useQuery(
    api.functions.useCases.admin.userManagement.invitations.list,
    {
      search: searchTerm || "",
      role: roleFilter || [],
      limit: pagination.pageSize,
      offset: pagination.pageIndex * pagination.pageSize,
    }
  );

  const cancelInvitation = useMutation(
    api.functions.useCases.admin.userManagement.invitations.cancel
  );
  const resendInvitation = useMutation(
    api.functions.useCases.admin.userManagement.invitations.resend
  );
  const bulkCancelInvitations = useMutation(
    api.functions.useCases.admin.userManagement.invitations.bulkCancel
  );
  const bulkResendInvitations = useMutation(
    api.functions.useCases.admin.userManagement.invitations.bulkResend
  );

  const invitations = invitationsData?.data || [];
  const totalCount = invitationsData?.total || 0;

  const columns: ColumnDef<DTOs.IInvitation>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <div className="flex items-center justify-center">
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center justify-center">
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "email",
      header: t("admin.invitationManagement.email"),
      cell: ({ row }) => {
        const invitation = row.original;
        return (
          <div className="flex items-center space-x-3">
            <div className="flex flex-col">
              <span className="font-medium">{invitation.email}</span>
            </div>
          </div>
        );
      },
      enableHiding: false,
    },
    {
      accessorKey: "roleSuggested",
      header: t("admin.invitationManagement.role"),
      cell: ({ row }) => {
        const invitation = row.original;
        const roles = invitation.roleSuggested || [];
        return (
          <div className="flex flex-wrap gap-1">
            {roles.map((role) => (
              <Badge key={role} variant="outline" className="text-xs">
                {role}
              </Badge>
            ))}
          </div>
        );
      },
    },
    {
      accessorKey: "accepted",
      header: t("admin.invitationManagement.status"),
      cell: ({ row }) => {
        const invitation = row.original;
        return <InvitationStatusBadge accepted={invitation.accepted} />;
      },
    },
    {
      accessorKey: "createdAt",
      header: t("admin.invitationManagement.createdAt"),
      cell: ({ row }) => {
        const invitation = row.original;
        const date = new Date(invitation.createdAt);
        return (
          <span className="text-sm text-muted-foreground">
            {date.toLocaleDateString()}
          </span>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const invitation = row.original;

        const handleCancelInvitation = async () => {
          const confirmationMessage = t(
            "admin.invitationManagement.confirmCancel"
          );
          if (!confirm(confirmationMessage)) {
            return;
          }

          try {
            await cancelInvitation({
              id: invitation._id,
            });
            toast.success(t("admin.invitationManagement.invitationCancelled"));
          } catch {
            toast.error(
              t("admin.invitationManagement.failedToCancelInvitation")
            );
          }
        };

        const handleResendInvitation = async () => {
          try {
            await resendInvitation({
              id: invitation._id,
            });
            toast.success(t("admin.invitationManagement.invitationResent"));
          } catch {
            toast.error(
              t("admin.invitationManagement.failedToResendInvitation")
            );
          }
        };

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="data-[state=open]:bg-muted text-muted-foreground flex size-8"
                size="icon"
              >
                <IconDotsVertical />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {!invitation.accepted && (
                <>
                  <DropdownMenuItem onClick={handleResendInvitation}>
                    <IconMail className="mr-2 h-4 w-4" />
                    {t("admin.invitationManagement.resendInvitation")}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                </>
              )}
              <DropdownMenuItem
                onClick={handleCancelInvitation}
                className="text-destructive focus:text-destructive"
              >
                <IconTrash className="mr-2 h-4 w-4" />
                {t("admin.invitationManagement.cancelInvitation")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: invitations,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      pagination,
    },
    pageCount: Math.ceil(totalCount / pagination.pageSize),
    manualPagination: true,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {Object.keys(rowSelection).length > 0 && (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsBulkResendDialogOpen(true)}
          >
            <IconRefresh className="mr-2 h-4 w-4" />
            {t("admin.invitationManagement.bulkResend")} (
            {Object.keys(rowSelection).length})
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsBulkCancelDialogOpen(true)}
          >
            <IconTrash className="mr-2 h-4 w-4" />
            {t("admin.invitationManagement.bulkCancel")} (
            {Object.keys(rowSelection).length})
          </Button>
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {invitations.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <span className="text-muted-foreground">
                    {t("admin.invitationManagement.noInvitationsFound")}
                  </span>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-2">
        <div className="flex-1 text-sm text-muted-foreground">
          {Object.keys(rowSelection).length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Rows per page</p>
            <select
              value={pagination.pageSize}
              onChange={(e) => {
                setPagination({
                  ...pagination,
                  pageSize: Number(e.target.value),
                  pageIndex: 0,
                });
              }}
              className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
            >
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
          </div>
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Page {pagination.pageIndex + 1} of{" "}
            {Math.max(1, Math.ceil(totalCount / pagination.pageSize))}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => setPagination({ ...pagination, pageIndex: 0 })}
              disabled={pagination.pageIndex === 0}
            >
              <span className="sr-only">Go to first page</span>
              <IconChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() =>
                setPagination({
                  ...pagination,
                  pageIndex: pagination.pageIndex - 1,
                })
              }
              disabled={pagination.pageIndex === 0}
            >
              <span className="sr-only">Go to previous page</span>
              <IconChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() =>
                setPagination({
                  ...pagination,
                  pageIndex: pagination.pageIndex + 1,
                })
              }
              disabled={
                pagination.pageIndex >=
                Math.ceil(totalCount / pagination.pageSize) - 1
              }
            >
              <span className="sr-only">Go to next page</span>
              <IconChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() =>
                setPagination({
                  ...pagination,
                  pageIndex: Math.ceil(totalCount / pagination.pageSize) - 1,
                })
              }
              disabled={
                pagination.pageIndex >=
                Math.ceil(totalCount / pagination.pageSize) - 1
              }
            >
              <span className="sr-only">Go to last page</span>
              <IconChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Dialogs */}
      <BulkCancelInvitationsDialog
        invitationIds={Object.keys(rowSelection)
          .map((index) => invitations[parseInt(index)])
          .filter((invitation): invitation is DTOs.IInvitation =>
            Boolean(invitation)
          )
          .map((invitation) => invitation._id)}
        open={isBulkCancelDialogOpen}
        onOpenChange={setIsBulkCancelDialogOpen}
        onSuccess={() => {
          setRowSelection({});
        }}
      />

      <BulkResendInvitationsDialog
        invitationIds={Object.keys(rowSelection)
          .map((index) => invitations[parseInt(index)])
          .filter((invitation): invitation is DTOs.IInvitation =>
            Boolean(invitation)
          )
          .map((invitation) => invitation._id)}
        open={isBulkResendDialogOpen}
        onOpenChange={setIsBulkResendDialogOpen}
        onSuccess={() => {
          setRowSelection({});
        }}
      />
    </div>
  );
}
