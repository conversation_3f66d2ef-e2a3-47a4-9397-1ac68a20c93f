"use client";

import * as React from "react";
import { usePathname } from "next/navigation";

import { NavDocuments } from "@curatd/ui/components/nav-documents";
import { NavMain } from "@curatd/ui/components/nav-main";
import { NavSecondary } from "@curatd/ui/components/nav-secondary";
import { NavUser } from "@curatd/ui/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@curatd/ui/components/sidebar";
import CuratdLogo from "./curatd-logo";
import { useAdminNavigation } from "../hooks/use-admin-navigation";
import Link from "next/link";
import { useCurrentLocale } from "@curatd/shared/locales/client";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { navItems, isNavItemActive } = useAdminNavigation();

  const locale = useCurrentLocale();
  const pathname = usePathname();

  const data = {
    navMain: navItems,
    // navClouds: [
    //   {
    //     title: "Capture",
    //     icon: IconCamera,
    //     isActive: true,
    //     url: "#",
    //     items: [
    //       {
    //         title: "Active Proposals",
    //         url: "#",
    //       },
    //       {
    //         title: "Archived",
    //         url: "#",
    //       },
    //     ],
    //   },
    //   {
    //     title: "Proposal",
    //     icon: IconFileDescription,
    //     url: "#",
    //     items: [
    //       {
    //         title: "Active Proposals",
    //         url: "#",
    //       },
    //       {
    //         title: "Archived",
    //         url: "#",
    //       },
    //     ],
    //   },
    //   {
    //     title: "Prompts",
    //     icon: IconFileAi,
    //     url: "#",
    //     items: [
    //       {
    //         title: "Active Proposals",
    //         url: "#",
    //       },
    //       {
    //         title: "Archived",
    //         url: "#",
    //       },
    //     ],
    //   },
    // ],
    // navSecondary: [
    //   {
    //     title: "Settings",
    //     url: "#",
    //     icon: IconSettings,
    //   },
    //   {
    //     title: "Get Help",
    //     url: "#",
    //     icon: IconHelp,
    //   },
    //   {
    //     title: "Search",
    //     url: "#",
    //     icon: IconSearch,
    //   },
    // ],
    // documents: [
    //   {
    //     name: "Data Library",
    //     url: "#",
    //     icon: IconDatabase,
    //   },
    //   {
    //     name: "Reports",
    //     url: "#",
    //     icon: IconReport,
    //   },
    //   {
    //     name: "Word Assistant",
    //     url: "#",
    //     icon: IconFileWord,
    //   },
    // ],
  };

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-0"
            >
              <Link href={`/${locale}/`}>
                <CuratdLogo variant="admin" showIcon />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain
          items={data.navMain}
          pathname={pathname}
          isNavItemActive={isNavItemActive}
        />
        {/* <NavDocuments items={data.documents} /> */}
        {/* <NavSecondary items={data.navSecondary} className="mt-auto" /> */}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
