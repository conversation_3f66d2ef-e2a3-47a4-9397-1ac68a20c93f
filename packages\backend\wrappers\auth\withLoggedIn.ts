import { QueryCtx, MutationCtx } from "../../convex/_generated/server";
import { userHasRole } from "../../helpers/auth/userHasRole";
import { getCurrentUser } from "../../helpers/auth/getCurrentUser";
import { Roles } from "../../types/enums";
import { Doc } from "../../convex/_generated/dataModel";

export function withLoggedIn<T extends QueryCtx | MutationCtx, R>(func: (ctx: T, user: Doc<"users">, ...args: any[]) => Promise<R>) {
    return async (ctx: T, args: any) => {
        const user = await getCurrentUser(ctx);
        if (!user) {
            throw new Error("User not found");
        }
        return func(ctx, user, args);
    }
}