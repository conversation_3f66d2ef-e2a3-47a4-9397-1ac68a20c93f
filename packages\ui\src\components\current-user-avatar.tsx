"use client";

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@curatd/ui/components/avatar";

export const CurrentUserAvatar = ({
  name,
  image,
}: {
  name?: string;
  image?: string;
}) => {
  const initials = name
    ?.split(" ")
    ?.map((word) => word[0])
    ?.join("")
    ?.toUpperCase();

  return (
    <Avatar>
      {image && <AvatarImage src={image} alt={initials} />}
      <AvatarFallback>{initials}</AvatarFallback>
    </Avatar>
  );
};
