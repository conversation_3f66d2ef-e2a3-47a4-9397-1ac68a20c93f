import * as React from "react";
import { IconSearch, IconX } from "@tabler/icons-react";
import { useI18n } from "@curatd/shared/locales/client";

import { Button } from "@curatd/ui/components/button";
import { Input } from "@curatd/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@curatd/ui/components/select";
import { Enums } from "@curatd/backend";

// Convert the union type to an array of strings without explicitly listing values
const USER_ROLES = Object.values(Enums.Roles);

interface InvitationFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  roleFilter: Enums.Roles[];
  onRoleFilterChange: (value: Enums.Roles[]) => void;
}

export function InvitationFilters({
  searchTerm,
  onSearchChange,
  roleFilter,
  onRoleFilterChange,
}: InvitationFiltersProps) {
  const t = useI18n();
  const roles = USER_ROLES;

  const hasActiveFilters = roleFilter.length > 0 || searchTerm.trim() !== "";
  const activeFiltersCount = [
    roleFilter.length > 0,
    searchTerm.trim() !== "",
  ].filter(Boolean).length;

  const clearAllFilters = () => {
    onSearchChange("");
    onRoleFilterChange([]);
  };

  return (
    <div className="border-b bg-background">
      {/* Filters Section */}
      <div className="border-t px-6 py-4">
        <div className="flex flex-col gap-4">
          {/* Primary Filters Row */}
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {/* Search */}
            <div className="lg:col-span-2">
              <div className="relative">
                <IconSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder={t("admin.invitationManagement.search")}
                  value={searchTerm}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="pl-9 pr-9"
                />
                {searchTerm && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 p-0 hover:bg-muted"
                    onClick={() => onSearchChange("")}
                  >
                    <IconX className="h-3 w-3" />
                    <span className="sr-only">Clear search</span>
                  </Button>
                )}
              </div>
            </div>

            {/* Role Filter */}
            <div>
              <Select
                value={roleFilter.join(",")}
                onValueChange={(value) =>
                  onRoleFilterChange(value.split(",") as Enums.Roles[])
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue
                    placeholder={t("admin.invitationManagement.allRoles")}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {t("admin.invitationManagement.allRoles")}
                  </SelectItem>
                  {roles.map((role) => (
                    <SelectItem key={role} value={role}>
                      <span>{role}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters & Clear Action Row */}
          <div className="flex items-center justify-between min-h-[32px]">
            <div className="flex items-center gap-2">
              {hasActiveFilters ? (
                <>
                  <span className="text-sm text-muted-foreground">
                    {activeFiltersCount > 1
                      ? `${activeFiltersCount} ${t("admin.invitationManagement.filters_plural")} ${t("admin.invitationManagement.applied_plural")}`
                      : `${activeFiltersCount} ${t("admin.invitationManagement.filters")} ${t("admin.invitationManagement.applied")}`}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="h-8 px-3 text-xs text-muted-foreground hover:text-foreground"
                  >
                    <IconX className="mr-1 h-3 w-3" />
                    {t("admin.invitationManagement.clear")}
                  </Button>
                </>
              ) : (
                <span className="text-sm text-muted-foreground">
                  {t("admin.invitationManagement.noFiltersApplied")}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
