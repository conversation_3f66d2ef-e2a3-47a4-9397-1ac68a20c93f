import { roleValidator } from "../../../../enumValidators";
import { doc } from "convex-helpers/validators";
import { Infer, v } from "convex/values";
import { Nullable } from "../../../../../helpers/nullableObjectReturnValidator";
import { Doc } from "../../../../../convex/_generated/dataModel"
import schema from "../../../../../convex/schema";

/*──────────────────────── GENERAL ────────────────────────*/

export const UserResponseDTOValidator = {
    ...(() => {
        // We don't want to return the hashed password or providerId so we manually omit them
        const { hashedPassword, providerId, ...ret } = doc(schema, "users").fields;
        return ret;
    })(),
    customer: v.optional(v.object(doc(schema, "customers").fields)),
    coach: v.optional(v.object(doc(schema, "coaches").fields)),
    facilityManager: v.optional(v.object(doc(schema, "facilityManagers").fields)),
    managedFacilities: v.optional(v.array(v.object(doc(schema, "facilities").fields))),
}

export const UserResponseDTOObjectValidator = v.object(UserResponseDTOValidator);

export interface ICompleteUser extends Doc<"users"> {
    customer?: Doc<"customers">;
    coach?: Doc<"coaches">;
    facilityManager?: Doc<"facilityManagers">;
    managedFacilities?: Doc<"facilities">[];
};

/*──────────────────────── LIST ────────────────────────*/

// Validator for the request body
export const UsersListRequestDTOValidator = {
    search: v.string(),
    role: v.array(roleValidator),
    limit: v.number(),
    offset: v.number(),
}

// Validator for the response body
export const UsersListResponseDTOValidator = {
    data: v.array(v.object(UserResponseDTOValidator)),
    total: v.number(),
}

// Extra validators to generate the types from the validators
export const UsersListRequestDTOObjectValidator = v.object(UsersListRequestDTOValidator);
export const UsersListResponseDTOObjectValidator = v.object(UsersListResponseDTOValidator);


// Type for the request body
export type UsersListRequestDTO = Infer<typeof UsersListRequestDTOObjectValidator>;
// Type for the response body
export type UsersListResponseDTO = Infer<typeof UsersListResponseDTOObjectValidator>;


/*──────────────────────── GET ────────────────────────*/

// Validator for the request body
export const UsersGetRequestDTOValidator = {
    id: v.id("users"),
}

// Validator for the response body
export const UsersGetResponseDTOValidator = UserResponseDTOValidator;

// Extra validators to generate the types from the validators
export const UsersGetRequestDTOObjectValidator = v.object(UsersGetRequestDTOValidator);
export const UsersGetResponseDTOObjectValidator = v.object(UsersGetResponseDTOValidator);

// Type for the request body
export type UsersGetRequestDTO = Infer<typeof UsersGetRequestDTOObjectValidator>;
// Type for the response body
export type UsersGetResponseDTO = Infer<typeof UsersGetResponseDTOObjectValidator>;


/*──────────────────────── TOGGLE ACTIVE ────────────────────────*/

// Validator for the request body
export const UsersToggleActiveRequestDTOValidator = {
    id: v.id("users"),
}

// Validator for the response body
export const UsersToggleActiveResponseDTOValidator = v.null();

// Extra validators to generate the types from the validators
export const UsersToggleActiveRequestDTOObjectValidator = v.object(UsersToggleActiveRequestDTOValidator);
export const UsersToggleActiveResponseDTOObjectValidator = UsersToggleActiveResponseDTOValidator;

// Type for the request body
export type UsersToggleActiveRequestDTO = Infer<typeof UsersToggleActiveRequestDTOObjectValidator>;
// Type for the response body
export type UsersToggleActiveResponseDTO = Infer<typeof UsersToggleActiveResponseDTOObjectValidator>;


/*──────────────────────── BULK TOGGLE ACTIVE ────────────────────────*/

// Validator for the request body
export const UsersBulkToggleActiveRequestDTOValidator = {
    ids: v.array(v.id("users")),
}

// Validator for the response body
export const UsersBulkToggleActiveResponseDTOValidator = v.number();

// Extra validators to generate the types from the validators
export const UsersBulkToggleActiveRequestDTOObjectValidator = v.object(UsersBulkToggleActiveRequestDTOValidator);
export const UsersBulkToggleActiveResponseDTOObjectValidator = UsersBulkToggleActiveResponseDTOValidator;

// Type for the request body
export type UsersBulkToggleActiveRequestDTO = Infer<typeof UsersBulkToggleActiveRequestDTOObjectValidator>;
// Type for the response body
export type UsersBulkToggleActiveResponseDTO = Infer<typeof UsersBulkToggleActiveResponseDTOObjectValidator>;


/*──────────────────────── ASSIGN ROLES ────────────────────────*/

// Validator for the request body
export const UsersAssignRolesRequestDTOValidator = {
    user_id: v.id("users"),
    roles: v.array(roleValidator),
}

// Validator for the response body
export const UsersAssignRolesResponseDTOValidator = v.null();

// Extra validators to generate the types from the validators
export const UsersAssignRolesRequestDTOObjectValidator = v.object(UsersAssignRolesRequestDTOValidator);
export const UsersAssignRolesResponseDTOObjectValidator = UsersAssignRolesResponseDTOValidator;


// Type for the request body
export type UsersAssignRolesRequestDTO = Infer<typeof UsersAssignRolesRequestDTOObjectValidator>;
// Type for the response body
export type UsersAssignRolesResponseDTO = Infer<typeof UsersAssignRolesResponseDTOObjectValidator>;


/*──────────────────────── REVOKE ROLES ────────────────────────*/

// Validator for the request body
export const UsersRevokeRolesRequestDTOValidator = {
    user_id: v.id("users"),
    roles: v.array(roleValidator),
}

// Validator for the response body
export const UsersRevokeRolesResponseDTOValidator = v.null();

// Extra validators to generate the types from the validators
export const UsersRevokeRolesRequestDTOObjectValidator = v.object(UsersRevokeRolesRequestDTOValidator);
export const UsersRevokeRolesResponseDTOObjectValidator = UsersRevokeRolesResponseDTOValidator;

// Type for the request body
export type UsersRevokeRolesRequestDTO = Infer<typeof UsersRevokeRolesRequestDTOObjectValidator>;
// Type for the response body
export type UsersRevokeRolesResponseDTO = Infer<typeof UsersRevokeRolesResponseDTOObjectValidator>;


/*──────────────────────── BULK ASSIGN ROLES ────────────────────────*/

// Validator for the request body
export const UsersBulkAssignRolesRequestDTOValidator = {
    user_ids: v.array(v.id("users")),
    roles: v.array(roleValidator),
};
// Validator for the response body
export const UsersBulkAssignRolesResponseDTOValidator = {
    success: v.boolean(),
    errors: v.array(v.string())
};

// Extra validators to generate the types from the validators
export const UsersBulkAssignRolesRequestDTOObjectValidator = v.object(UsersBulkAssignRolesRequestDTOValidator);
export const UsersBulkAssignRolesResponseDTOObjectValidator = v.object(UsersBulkAssignRolesResponseDTOValidator);

// Type for the request body
export type UsersBulkAssignRolesRequestDTO = Infer<typeof UsersBulkAssignRolesRequestDTOObjectValidator>;
// Type for the response body
export type UsersBulkAssignRolesResponseDTO = Infer<typeof UsersBulkAssignRolesResponseDTOObjectValidator>;


/*──────────────────────── BULK REVOKE ROLES ────────────────────────*/

// Validator for the request body
export const UsersBulkRevokeRolesRequestDTOValidator = {
    user_ids: v.array(v.id("users")),
    roles: v.array(roleValidator),
};
// Validator for the response body
export const UsersBulkRevokeRolesResponseDTOValidator = {
    success: v.boolean(),
    errors: v.array(v.string())
};

// Extra validators to generate the types from the validators
export const UsersBulkRevokeRolesRequestDTOObjectValidator = v.object(UsersBulkRevokeRolesRequestDTOValidator);
export const UsersBulkRevokeRolesResponseDTOObjectValidator = v.object(UsersBulkRevokeRolesResponseDTOValidator);

// Type for the request body
export type UsersBulkRevokeRolesRequestDTO = Infer<typeof UsersBulkRevokeRolesRequestDTOObjectValidator>;
// Type for the response body
export type UsersBulkRevokeRolesResponseDTO = Infer<typeof UsersBulkRevokeRolesResponseDTOObjectValidator>;
