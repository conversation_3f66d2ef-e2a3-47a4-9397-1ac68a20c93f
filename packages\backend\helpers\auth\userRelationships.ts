import { QueryCtx, MutationCtx } from "../../convex/_generated/server";
import { Doc } from "../../convex/_generated/dataModel";

export const loadUserRelationships = async (ctx: QueryCtx | MutationCtx, user: Doc<"users">) => {
    // Load each relationship independently to avoid array index issues
    const customer = user.customerId ? await ctx.db.get(user.customerId) : null;
    const coach = user.coachId ? await ctx.db.get(user.coachId) : null;

    let facilityManager = null;
    let managedFacilities = null;

    if (user.facilityManagerId) {
        const facilityManagerId = user.facilityManagerId;
        facilityManager = await ctx.db.get(facilityManagerId);
        managedFacilities = await ctx.db
            .query("facilities")
            .withIndex("by_manager", q => q.eq("facilityManagerId", facilityManagerId))
            .collect();
    }

    return {
        customer: customer || undefined,
        coach: coach || undefined,
        facilityManager: facilityManager || undefined,
        managedFacilities: managedFacilities || undefined
    } as {
        customer?: Doc<"customers">,
        coach?: Doc<"coaches">,
        facilityManager?: Doc<"facilityManagers">,
        managedFacilities?: Doc<"facilities">[],
    };
};