import { QueryCtx, MutationCtx } from "../../convex/_generated/server";
import { Doc } from "../../convex/_generated/dataModel";

export const loadUserRelationships = async (ctx: QueryCtx | MutationCtx, user: Doc<"users">) => {
    // Create promises for all relationships that exist, running them in parallel
    const promises = {
        customer: user.customerId ? ctx.db.get(user.customerId) : Promise.resolve(null),
        coach: user.coachId ? ctx.db.get(user.coachId) : Promise.resolve(null),
        facilityManager: user.facilityManagerId ? ctx.db.get(user.facilityManagerId) : Promise.resolve(null),
        managedFacilities: user.facilityManagerId
            ? ctx.db
                .query("facilities")
                .withIndex("by_manager", q => q.eq("facilityManagerId", user.facilityManagerId))
                .collect()
            : Promise.resolve(null),
    };

    // Wait for all promises to resolve in parallel
    const results = await Promise.all([
        promises.customer,
        promises.coach,
        promises.facilityManager,
        promises.managedFacilities,
    ]);

    // Destructure with explicit names to avoid index confusion
    const [customer, coach, facilityManager, managedFacilities] = results;

    return {
        customer: customer || undefined,
        coach: coach || undefined,
        facilityManager: facilityManager || undefined,
        managedFacilities: managedFacilities || undefined
    } as {
        customer?: Doc<"customers">,
        coach?: Doc<"coaches">,
        facilityManager?: Doc<"facilityManagers">,
        managedFacilities?: Doc<"facilities">[],
    };
};